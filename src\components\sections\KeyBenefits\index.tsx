import React from "react";
import Container from "../../Container";

const benefits = [
  {
    title: "Cepat & SEO-friendly",
    desc: "Dibangun dengan Next.js 15 dan optimasi modern.",
  },
  {
    title: "Mudah dikustomisasi",
    desc: "Tailwind CSS memudahkan styling yang konsisten.",
  },
  {
    title: "Responsif",
    desc: "Tampilan optimal di semua perangkat.",
  },
];

export default function KeyBenefits() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Key Benefits</h2>
        <div className="mt-8 grid gap-6 sm:grid-cols-3">
          {benefits.map((b) => (
            <div key={b.title} className="rounded-xl border p-6 bg-white/50 dark:bg-zinc-900/50">
              <h3 className="font-heading font-semibold text-lg">{b.title}</h3>
              <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-300">{b.desc}</p>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}

