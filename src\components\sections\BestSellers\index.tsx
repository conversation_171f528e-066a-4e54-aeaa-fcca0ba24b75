import React from "react";
import Container from "../../Container";

const items = [
  { name: "Produk A", desc: "Deskripsi singkat produk A", price: "Rp 149.000" },
  { name: "Produk B", desc: "Deskripsi singkat produk B", price: "Rp 199.000" },
  { name: "Produk C", desc: "Deskripsi singkat produk C", price: "Rp 249.000" },
];

export default function BestSellers() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Best Sellers</h2>
        <div className="mt-6 grid gap-6 sm:grid-cols-3">
          {items.map((p) => (
            <div key={p.name} className="rounded-xl border p-6">
              <div className="aspect-video rounded-lg bg-zinc-100 dark:bg-zinc-800" />
              <h3 className="mt-4 font-heading font-semibold">{p.name}</h3>
              <p className="text-sm text-zinc-600 dark:text-zinc-300">{p.desc}</p>
              <div className="mt-3 font-medium">{p.price}</div>
              <button className="mt-4 inline-flex h-10 items-center justify-center rounded-md bg-black text-white px-4 text-sm hover:opacity-90">
                Beli
              </button>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}

